<!-- pages/profile/profile.wxml -->
<scroll-view class="page-scroll" scroll-y="{{true}}" show-scrollbar="{{false}}" enhanced="{{true}}" bounces="{{false}}">
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info">
    <!-- 基本信息 -->
    <view class="avatar-section" bind:tap="goProfile">
      <view class="left-section" catch:tap="goProfile">
        <van-image round width="120rpx" height="120rpx" src="{{userInfo.avatar || '/static/img/default_avatar.png'}}" custom-class="user-avatar" />
        <view class="user-details">
          <view class="username">
            {{userInfo.nickname}}
          </view>
          <view class="user-id">
            <block wx:if="{{hasLogin}}">
              <view class="user-info-rows">
                <view class="info-row residential-info">
                  <van-icon name="wap-home-o" size="14px" color="#666" />
                  <text class="info-text">{{userInfo.currentResidentialName || '未绑定小区'}}</text>
                </view>
                <view class="info-row stats-row">
                  <view class="stat-item">
                    <van-icon name="friends-o" size="14px" color="#666" />
                    <text class="info-text">关注 {{followCount}}</text>
                  </view>
                  <view class="stats-divider"></view>
                  <view class="stat-item">
                    <van-icon name="like-o" size="14px" color="#666" />
                    <text class="info-text">粉丝 {{fansCount}}</text>
                  </view>
                </view>
                <!-- 个性签名 -->
                <view class="info-row signature-row" catch:tap="showSloganPopup">
                  <!-- <van-icon name="comment-o" size="14px" color="#666" /> -->
                  <text class="info-text signature-text">{{userInfo.slogan || '添加个性签名 (最多15字)'}}</text>
                </view>
              </view>
            </block>
            <view wx:else class="login-button" catch:tap="onLoginTipClick">
              <text>登录/注册</text>
              <van-icon name="arrow" class="login-arrow" />
            </view>
          </view>
        </view>
      </view>
      <view class="edit-button" catch:tap="goProfile">
        <van-icon name="setting-o" size="20px" color="#999" />
      </view>
    </view>
  </view>

  <!-- 碳豆功能区域 -->
  <view class="silver-section">
    <!-- 顶部碳豆余额卡片 -->
    <view class="balance-card">
      <view class="function-header">
        <view class="function-title">
          <!-- <van-icon name="gold-coin-o" size="16px" color="#333" /> -->
          <text>我的碳豆</text>
        </view>
        <view class="more-task-btn" bindtap="viewDetail">
          <text>碳豆明细</text>
          <van-icon name="arrow" size="12px" color="#999" />
        </view>
      </view>
      <view class="balance-wrapper">
        <image src="/static/img/coin.png" mode="aspectFit" class="coin-icon" />
        <view class="balance-info">
          <view class="balance-amount">{{userInfo.silver}}</view>
          <!-- <view class="balance-subtitle">累计免费获得 {{totalEarned}} 碳豆</view> -->
        </view>
      </view>
    </view>

    <!-- 功能卡片 -->
    <view class="balance-card function-card">
      <!-- <view class="-hefunctionader">
        <view class="function-title">
          <van-icon name="apps-o" size="16px" color="#333" />
          <text>数据中心</text>
        </view>
        <view class="function-more" bindtap="showAllServices">
          <text>更多</text>
          <van-icon name="arrow" size="12px" color="#999" />
        </view>
      </view> -->
      <view class="function-grid function-grid-four">
        <view class="function-item" hover-class="function-item-hover" bindtap="navigateToPage" data-page="idle">
          <view class="function-icon">
            <view class="function-badge-large">{{productCount}}</view>
          </view>
          <view class="function-info">
            <view class="function-name-row">
              <view class="function-name">我发布的</view>
            </view>
          </view>
        </view>
        <view class="function-item" hover-class="function-item-hover" bindtap="navigateToPage" data-page="favorite">
          <view class="function-icon">
            <view class="function-badge-large">{{collectionCount}}</view>
          </view>
          <view class="function-info">
            <view class="function-name-row">
              <view class="function-name">我的收藏</view>
            </view>
          </view>
        </view>
        <view class="function-item" hover-class="function-item-hover" bindtap="navigateToPage" data-page="follow">
          <view class="function-icon">
            <view class="function-badge-large">{{followCount}}</view>
          </view>
          <view class="function-info">
            <view class="function-name-row">
              <view class="function-name">我的关注</view>
            </view>
          </view>
        </view>
        <view class="function-item" hover-class="function-item-hover" bindtap="navigateToPage" data-page="activity">
          <view class="function-icon">
            <view class="function-badge-large">{{fansCount}}</view>
          </view>
          <view class="function-info">
            <view class="function-name-row">
              <view class="function-name">我的活动</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 交易记录卡片 -->
    <view class="balance-card function-card">
      <!-- <view class="function-header">
        <view class="function-title">
          <van-icon name="orders-o" size="16px" color="#333" />
          <text>交易记录</text>
        </view>
      </view> -->
      <view class="function-grid" style="grid-template-columns: repeat(2, 1fr);">
        <view class="function-item" hover-class="function-item-hover" bindtap="navigateToPage" data-page="bought">
          <view class="function-icon">
            <view class="function-badge-large">{{boughtCount}}</view>
          </view>
          <view class="function-info">
            <view class="function-name-row">
              <view class="function-name">我买入的</view>
            </view>
          </view>
        </view>
        <view class="function-item" hover-class="function-item-hover" bindtap="navigateToPage" data-page="sold">
          <view class="function-icon">
            <view class="function-badge-large">{{soldCount}}</view>
          </view>
          <view class="function-info">
            <view class="function-name-row">
              <view class="function-name">我卖出的</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 任务列表区域 -->
    <view class="earn-more-section">
      <view class="function-header">
        <view class="function-title">
          <!-- <van-icon name="gift-o" size="16px" color="#3671FF" /> -->
          <text>完成任务得碳豆</text>
        </view>
        <view class="more-task-btn" bindtap="navigateToPage" data-page="taskList">
          <text>更多任务</text>
          <van-icon name="arrow" size="12px" color="#999" />
        </view>
      </view>
      
      <view class="balance-card function-card">
        <view class="task-list-container">
          <view class="task-grid">
            <view class="task-grid-item" wx:for="{{tasks}}" wx:key="id" wx:if="{{index < 3}}">
              <view class="task-grid-content">
                <view class="task-grid-reward">
                  <text class="reward-amount">{{item.rewardSilver || 0}}</text>
                  <text class="reward-label">碳豆</text>
                </view>
                <view class="task-grid-info">
                  <!-- <view class="task-grid-title">{{item.taskName || '任务名称'}}</view> -->
                  <view class="task-grid-desc">{{item.taskDesc || '任务描述'}}</view>
                  <view class="task-grid-progress">
                    <text class="progress-text">{{item.currentCount || 0}}/{{item.targetCount || 0}}</text>
                    <view class="progress-bar">
                      <view class="progress-fill" style="width: {{(item.progressPercent || 0)}}%;"></view>
                    </view>
                  </view>
                </view>
                <view class="task-grid-action">
                  <button wx:if="{{item.canClaim}}" 
                          class="action-btn claim-btn" 
                          bindtap="claimReward" 
                          data-task-id="{{item.id}}">
                    立即领取
                  </button>
                  <button wx:elif="{{item.isCompleted}}" 
                          class="action-btn completed-btn" 
                          disabled>
                    已完成
                  </button>
                  <button wx:else 
                          class="action-btn progress-btn" 
                          bindtap="goToTask" 
                          data-task-code="{{item.taskCode}}">
                    去完成
                  </button>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-task-state" wx:if="{{!taskLoading && tasks.length === 0}}">
            <van-icon name="gift-o" size="40px" color="#ccc" />
            <text class="empty-text">暂无任务</text>
          </view>
        </view>
      </view>
    </view>

    <view class="earn-more-section">
      <view class="function-header">
        <view class="function-title">
          <!-- <van-icon name="gift-o" size="16px" color="#3671FF" /> -->
          <text>获取更多碳豆</text>
        </view>
      </view>
      
      <view class="balance-card function-card">
        <view class="quick-task-list">
          <view class="task-item" wx:for="{{quickTasks}}" wx:key="id" bindtap="handleQuickTask" data-id="{{item.id}}" hover-class="button-hover">
            <view class="task-left">
              <image class="task-icon" src="{{item.icon}}" mode="aspectFit" />
              <view class="task-info">
                <view class="task-name">{{item.name}}</view>
                <view class="task-desc">{{item.desc}}</view>
              </view>
            </view>
            <button class="task-btn" hover-class="button-hover" open-type="{{item.type === 'share' ? 'share' : ''}}" catchtap="handleQuickTask" data-id="{{item.id}}">
              {{item.action}}
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 客服与反馈区域 -->
    <view class="service-section">
      <view class="balance-card function-card">
        <view class="service-grid">
          <view class="service-button" hover-class="function-item-hover" bindtap="contactCustomerService">
            <view class="service-icon">
              <van-icon name="service-o" size="24px" color="#666" />
            </view>
            <view class="service-name">联系客服</view>
          </view>
          <view class="service-button" hover-class="function-item-hover" bindtap="userFeedback">
            <view class="service-icon">
              <van-icon name="comment-o" size="24px" color="#666" />
            </view>
            <view class="service-name">意见反馈</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <login-action id="loginAction" bind:loginSuccess="onLoginSuccess" bind:loginFail="onLoginFail"></login-action>

 <!-- 个性签名修改弹窗 -->
  <van-popup show="{{ showSloganPopup }}" round position="bottom" custom-style="padding: 24px 0 32px 0; border-radius: 24px 24px 0 0;" bind:close="onSloganPopupClose" safe-area-inset-bottom>
    <view class="slogan-popup-content">
      <view class="slogan-popup-close" bind:tap="onSloganPopupClose">
        <van-icon name="cross" size="20px" color="#999" />
      </view>
      <view class="slogan-popup-header">
        <text class="slogan-popup-title">炫炫座右铭</text>
        <text class="slogan-popup-subtitle">个性签名将在个人主页展示</text>
      </view>
      <view class="slogan-popup-input">
        <van-field value="{{ newSlogan }}" bind:input="onSloganChange" bind:compositionstart="onCompositionStart" bind:compositionend="onCompositionEnd" placeholder="写下你的个性签名" placeholder-style="color: #999;" border="{{ false }}" type="text" custom-style="padding: 12px 24px; background: #f7f8fa; border-radius: 8px;" input-class="slogan-input">
          <view slot="right-icon" style="color: {{newSlogan.length > 12 ? '#ff6b6b' : '#999'}}; font-size: 12px;">
            {{newSlogan.length}}/15
          </view>
        </van-field>
      </view>

      <view class="silver-info">
        <view class="silver-icon"></view>
        <text class="silver-text">当前共有 {{userInfo.silver}} 碳豆，10碳豆修改一次</text>
      </view>

      <view class="slogan-popup-btn">
        <van-button type="primary" block round custom-class="confirm-btn" bind:tap="handleChangeSlogan">保存修改</van-button>
      </view>
    </view>

  </van-popup>

</view>
</scroll-view>