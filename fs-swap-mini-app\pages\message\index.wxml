<view class="message-container">
  <!-- 简单刷新指示器 -->
  <view class="refresh-indicator {{showRefreshIndicator ? 'show' : 'hide'}}">
    <van-loading size="12px" color="#3B7FFF" />
    <text class="refresh-text">正在刷新...</text>
  </view>

  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-input-wrapper">
      <text class="iconfont icon-search search-icon"></text>
      <input 
        class="search-input" 
        placeholder="搜索" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
      />
      <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="clearSearch">
        <text class="iconfont icon-close"></text>
      </view>
    </view>
  </view>

  <!-- 会话列表 -->
  <scroll-view 
    class="conversation-list" 
    scroll-y 
    enhanced 
    show-scrollbar="{{false}}"
    refresher-enabled 
    refresher-triggered="{{isRefreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
  >
    <view class="conversation-items">
      <!-- 系统消息 -->
      <view 
        class="conversation-item {{item.isOfficial ? 'official' : ''}}"
        wx:for="{{systemMessages}}" 
        wx:key="conversationId"
        data-conversation="{{item}}"
        bindtap="onConversationTap"
      >
        <!-- 头像 -->
        <view class="avatar-wrapper">
          <view class="icon-avatar {{item.type}}">
            <van-icon name="{{item.icon}}" color="{{item.iconColor}}" size="24px" />
          </view>
          <view wx:if="{{item.isOfficial}}" class="official-badge">
            <text class="iconfont icon-check"></text>
          </view>
          <view wx:if="{{item.unreadCount > 0}}" class="unread-badge">
            {{item.unreadCount > 99 ? '99+' : item.unreadCount}}
          </view>
        </view>
        
        <!-- 会话信息 -->
        <view class="conversation-info">
          <view class="info-header">
            <text class="name">{{item.title}}</text>
            <text class="time">{{item.lastMessageTimeText}}</text>
          </view>
          <view class="last-message">{{item.lastMessageContent}}</view>
        </view>
      </view>

      <!-- 普通会话 -->
      <view 
        class="conversation-item"
        wx:for="{{conversationList}}" 
        wx:key="conversationId"
        data-conversation="{{item}}"
        bindtap="onConversationTap"
      >
        <!-- 头像 -->
        <view class="avatar-wrapper">
          <image 
            src="{{item.avatar || '/static/img/default_avatar.png'}}" 
            class="avatar"
            mode="aspectFill"
            binderror="onAvatarError"
            data-index="{{index}}"
          ></image>
          <view wx:if="{{item.unreadCount > 0}}" class="unread-badge">
            {{item.unreadCount > 99 ? '99+' : item.unreadCount}}
          </view>
        </view>
        
        <!-- 会话信息 -->
        <view class="conversation-info">
          <view class="info-header">
            <text class="name">{{item.title}}</text>
            <text class="time">{{item.lastMessageTimeText}}</text>
          </view>
          <view class="last-message">{{item.lastMessageContent}}</view>
        </view>

        <!-- 消息状态 -->
        <view wx:if="{{item.status}}" class="message-status">
          <text class="iconfont icon-{{item.status === 'sending' ? 'loading' : item.status === 'failed' ? 'warning' : 'check'}}"></text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{conversationList.length === 0 && systemMessages.length === 0 && !isLoading}}">
      <view class="empty-icon">💬</view>
      <text class="empty-text" wx:if="{{hasLogin}}">还没有聊天记录</text>
      <text class="empty-text" wx:else>登录后查看消息</text>
      <text class="empty-desc" wx:if="{{hasLogin}}">开始你的第一次对话吧</text>
      <text class="empty-desc" wx:else>登录后可以查看系统消息和聊天记录</text>
      <view wx:if="{{!hasLogin}}" class="login-tip" bindtap="showLoginComponent">
        <text class="login-btn-text">立即登录</text>
      </view>
    </view>

    <!-- 搜索无结果 -->
    <view class="empty-state" wx:if="{{searchKeyword && conversationList.length === 0 && systemMessages.length === 0 && !isLoading}}">
      <view class="empty-icon">🔍</view>
      <text class="empty-text">没有找到相关聊天</text>
      <text class="empty-desc">试试其他关键词</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <van-loading size="16px" color="#3B7FFF" />
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view> 

<!-- 登录组件 -->
<login-action 
  id="loginAction" 
  bind:loginSuccess="onLoginSuccess" 
  bind:loginFail="onLoginFail"
></login-action> 