# 社区互助发布类型字段重构文档

## 概述
为社区互助表 `community_help` 添加了 `publish_type` 字段，用于区分不同的发布类型（求助、提供帮助、互助交流等）。

## 数据库变更

### 1. 表结构变更
```sql
ALTER TABLE community_help 
ADD COLUMN publish_type VARCHAR(10) NOT NULL COMMENT '发布类型';
```

### 2. 字典数据初始化
执行 `sql/community_help_publish_type_dict.sql` 脚本，添加：
- 字典类型：`community_help_publish_type`
- 字典数据：
  - `help_request` - 求助
  - `help_offer` - 提供帮助  
  - `help_exchange` - 互助交流

## 后端代码修改

### 1. 枚举类型
- `fs-swap-common/src/main/java/com/fs/swap/common/enums/DictType.java`
  - 添加 `COMMUNITY_HELP_PUBLISH_TYPE("community_help_publish_type")`

### 2. 实体类
- `fs-swap-common/src/main/java/com/fs/swap/common/core/domain/entity/CommunityHelp.java`
  - 添加 `publishType` 字段
  - 添加对应的 getter/setter 方法
  - 更新 toString 方法

### 3. Mapper XML
- `fs-swap-system/src/main/resources/mapper/swap/CommunityHelpMapper.xml`
  - 更新 resultMap 映射
  - 更新 selectCommunityHelpVo SQL
  - 更新 insert 和 update 语句
  - 添加 publishType 查询条件

### 4. 微信端接口
- `fs-swap-wx-api/src/main/java/com/fs/swap/wx/controller/CommonController.java`
  - 添加发布类型字典数据到系统信息接口

## 前端代码修改

### 1. 管理端页面
- `fs-swap-ui/src/views/operation/communityHelp/index.vue`
  - 添加发布类型查询条件
  - 添加发布类型表格列
  - 添加发布类型表单字段
  - 添加发布类型详情显示
  - 添加发布类型表单验证
  - 修正字段映射问题（topicId -> category）

### 2. 微信小程序
- `fs-swap-mini-app/services/systemInfo.js`
  - 添加获取发布类型数据的方法
  - `getCommunityHelpPublishTypes()`
  - `getFormattedHelpPublishTypes()`
  - `getHelpPublishTypeNameMap()`

- `fs-swap-mini-app/pages/community-help-publish/index.js`
  - 添加发布类型相关数据字段
  - 添加 `loadPublishTypes()` 方法
  - 添加发布类型选择事件处理
  - 更新表单验证逻辑
  - 更新提交数据结构

- `fs-swap-mini-app/pages/community-help-publish/index.wxml`
  - 添加发布类型选择器UI
  - 添加发布类型弹窗

- `fs-swap-mini-app/pages/community-help/index.js`
  - 添加发布类型数据加载
  - 更新名称映射逻辑

## 功能特性

### 1. 发布类型选项
- **求助**：用户发布求助信息
- **提供帮助**：用户提供帮助服务
- **互助交流**：用户发布互助交流信息

### 2. 管理功能
- 管理员可以按发布类型筛选互助信息
- 支持发布类型的增删改查
- 表格和详情页面显示发布类型

### 3. 用户体验
- 发布时必须选择发布类型
- 列表页面显示发布类型标识
- 支持按发布类型筛选

## 注意事项

1. **数据迁移**：现有数据需要设置默认的发布类型值
2. **向后兼容**：确保现有功能不受影响
3. **字段验证**：发布类型为必填字段
4. **字典管理**：发布类型通过系统字典管理，便于扩展

## 测试建议

1. 测试新增互助信息时发布类型的选择和保存
2. 测试管理端按发布类型筛选功能
3. 测试微信端发布类型选择器的交互
4. 测试现有数据的兼容性
5. 测试字典数据的正确加载和显示
