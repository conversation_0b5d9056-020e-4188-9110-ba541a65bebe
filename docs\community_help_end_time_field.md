# 社区互助截止时间字段适配文档

## 概述
为社区互助表 `community_help` 添加了 `end_time` 字段，用于设置互助信息的截止时间。

## 数据库变更

### 表结构变更
```sql
ALTER TABLE community_help 
ADD COLUMN end_time DATETIME NOT NULL COMMENT '截止时间' AFTER create_time;
```

## 后端代码修改

### 1. 实体类
- `fs-swap-common/src/main/java/com/fs/swap/common/core/domain/entity/CommunityHelp.java`
  - 添加 `java.util.Date` 导入
  - 添加 `endTime` 字段，类型为 `Date`
  - 添加 `@Excel` 注解，支持导出功能
  - 添加对应的 getter/setter 方法
  - 更新 toString 方法

### 2. Mapper XML
- `fs-swap-system/src/main/resources/mapper/swap/CommunityHelpMapper.xml`
  - 更新 resultMap 映射，添加 `end_time` 字段映射
  - 更新 selectCommunityHelpVo SQL，包含 `end_time` 字段
  - 更新 insert 语句，支持 `end_time` 字段插入
  - 更新 update 语句，支持 `end_time` 字段更新

## 前端代码修改

### 1. 管理端页面
- `fs-swap-ui/src/views/operation/communityHelp/index.vue`
  - **查询条件**：添加截止时间范围查询
  - **表格列**：添加截止时间显示列
  - **表单字段**：添加截止时间选择器（datetime 类型）
  - **详情显示**：在详情对话框中显示截止时间
  - **数据处理**：
    - 添加 `daterangeEndTime` 日期范围变量
    - 在查询参数中添加 `endTime` 字段
    - 在表单重置中添加 `endTime` 字段
    - 在查询方法中处理截止时间范围参数
  - **表单验证**：添加截止时间必填验证规则

### 2. 微信小程序
- `fs-swap-mini-app/pages/community-help-publish/index.js`
  - **表单数据**：在 `formData` 中添加 `endTime` 字段
  - **UI 状态**：添加截止时间选择器相关状态变量
    - `showEndTimePicker`：控制选择器显示
    - `endTimeValue`：存储选择的时间值
    - `endTimeDisplay`：格式化显示的时间文本
  - **事件处理**：
    - `onEndTimeSelect()`：打开时间选择器
    - `onEndTimeConfirm()`：确认时间选择
    - `onEndTimePickerClose()`：关闭时间选择器
  - **表单验证**：在 `checkCanSubmit()` 中添加截止时间验证
  - **数据提交**：在提交数据中包含 `endTime` 字段

- `fs-swap-mini-app/pages/community-help-publish/index.wxml`
  - 添加截止时间选择条目
  - 添加截止时间选择器弹窗（使用 `van-datetime-picker`）
  - 设置最小时间为当前时间，防止选择过期时间

## 功能特性

### 1. 管理端功能
- ✅ 支持按截止时间范围筛选互助信息
- ✅ 表格中显示截止时间列
- ✅ 新增/编辑时可设置截止时间
- ✅ 详情页面显示截止时间
- ✅ 截止时间为必填字段
- ✅ 支持导出截止时间数据

### 2. 微信端功能
- ✅ 发布时必须选择截止时间
- ✅ 时间选择器限制不能选择过去时间
- ✅ 友好的时间显示格式
- ✅ 表单验证确保截止时间已选择

### 3. 数据处理
- ✅ 数据库字段类型为 `DATETIME`，支持精确到秒
- ✅ Java 实体使用 `Date` 类型
- ✅ 前端使用标准日期时间格式
- ✅ 支持日期时间范围查询

## 技术实现细节

### 1. 时间格式处理
- **数据库**：`DATETIME` 格式存储
- **Java**：`Date` 类型处理
- **管理端**：`yyyy-MM-dd HH:mm:ss` 格式显示
- **微信端**：`YYYY-MM-DD HH:mm` 格式显示

### 2. 表单验证
- **管理端**：使用 Element UI 的表单验证规则
- **微信端**：在 `checkCanSubmit()` 方法中验证

### 3. 时间选择器
- **管理端**：使用 `el-date-picker` 组件，类型为 `datetime`
- **微信端**：使用 `van-datetime-picker` 组件，限制最小时间

## 注意事项

1. **时区处理**：确保前后端时区一致
2. **数据迁移**：现有数据需要设置默认截止时间值
3. **业务逻辑**：可以基于截止时间实现自动过期功能
4. **用户体验**：时间选择器限制了过去时间的选择

## 后续扩展建议

1. **自动过期**：可以添加定时任务，自动将过期的互助信息标记为已过期
2. **提醒功能**：可以在截止时间临近时提醒用户
3. **时间显示**：可以添加相对时间显示（如"还有2天"）
4. **批量操作**：管理端可以支持批量修改截止时间
